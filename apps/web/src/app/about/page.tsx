'use client';

import { Award, Database, Globe, Lock, Shield, Users } from 'lucide-react';
import Link from 'next/link';

export default function AboutPage() {
  const dataSources = [
    "Property sales & title data (realestate.com.au, domain.com.au)",
    "Current listings & market activity",
    "Planning & zoning data",
    "Development applications",
    "Economic indicators (RBA, ABS)",
    "Infrastructure projects",
    "School performance (MySchool)",
    "Crime statistics",
    "Rental market analysis",
    "Transport & accessibility",
    "Climate & natural hazard risk",
    "Population & demographics",
    "Construction cost index",
    "Market sentiment & auctions",
    "Energy efficiency ratings",
    "Mortgage stress indicators",
    "Business & retail activity",
    "Social media & search trends"
  ];

  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Founder & CEO",
      description: "Property technology expert with 15+ years in real estate analytics and AI development.",
      image: "/api/placeholder/150/150"
    },
    {
      name: "<PERSON>",
      role: "Head of Data Science",
      description: "PhD in Machine Learning with expertise in property valuation algorithms and predictive modeling.",
      image: "/api/placeholder/150/150"
    },
    {
      name: "<PERSON>",
      role: "Head of Engineering",
      description: "Full-stack architect specializing in scalable real-time data processing systems.",
      image: "/api/placeholder/150/150"
    }
  ];

  return (
    <div className="bg-gradient-to-br from-blue-50 via-white to-indigo-50 min-h-screen">
      {/* Hero Section */}
      <section className="relative py-24 px-4 sm:px-6 lg:px-8 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-50/50 via-transparent to-indigo-50/50"></div>
        
        <div className="relative max-w-7xl mx-auto text-center">
          <div className="mb-8">
            <div className="inline-flex items-center px-4 py-2 bg-primary-50 border border-primary-200 rounded-full text-primary-700 text-sm font-medium mb-6">
              <span className="w-2 h-2 bg-primary-500 rounded-full mr-2 animate-pulse"></span>
              About Revalu
            </div>
          </div>

          <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-8 leading-tight">
            Revolutionizing Property
            <span className="block bg-gradient-to-r from-primary-600 to-indigo-600 bg-clip-text text-transparent">
              Intelligence
            </span>
          </h1>

          <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
            We're on a mission to democratize access to institutional-grade property intelligence, 
            making advanced analytics available to every property professional in Australia.
          </p>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl font-bold text-gray-900 mb-6">Our Mission</h2>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                To provide every property professional with access to the same level of market intelligence 
                that was previously only available to large institutions. We believe that better data leads 
                to better decisions, and better decisions create better outcomes for everyone in the property market.
              </p>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                    <Award className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-900 mb-2">Excellence</h3>
                    <p className="text-gray-600 text-sm">Delivering the highest quality data and insights</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-900 mb-2">Accessibility</h3>
                    <p className="text-gray-600 text-sm">Making professional tools available to everyone</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-primary-50 to-indigo-50 rounded-3xl p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Vision</h3>
              <p className="text-gray-700 leading-relaxed">
                To become Australia's most trusted source of property intelligence, empowering millions of 
                property decisions with AI-powered insights that reveal opportunities others miss.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">Meet Our Team</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              A passionate team of property experts, data scientists, and engineers working to transform 
              the Australian property market.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-lg p-8 text-center">
                <div className="w-24 h-24 bg-gradient-to-br from-primary-500 to-primary-700 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-white font-bold text-2xl">{member.name.split(' ').map(n => n[0]).join('')}</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h3>
                <p className="text-primary-600 font-medium mb-4">{member.role}</p>
                <p className="text-gray-600 text-sm leading-relaxed">{member.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Data Sources */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-blue-50 border border-blue-200 rounded-full text-blue-700 text-sm font-medium mb-6">
              <Database className="w-4 h-4 mr-2" />
              18+ Premium Data Sources
            </div>
            <h2 className="text-4xl font-bold text-gray-900 mb-6">Comprehensive Data Coverage</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our AI algorithm analyzes data from 18+ premium sources to deliver the most accurate 
              and comprehensive property intelligence available.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {dataSources.map((source, index) => (
              <div key={index} className="flex items-start p-4 bg-gray-50 rounded-xl">
                <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span className="text-gray-700 text-sm">{source}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Security & Privacy */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">Security & Privacy</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Your data security and privacy are our top priorities. We implement industry-leading 
              security measures to protect your information.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center p-8 bg-white rounded-2xl shadow-lg">
              <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Shield className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Data Encryption</h3>
              <p className="text-gray-600">All data is encrypted in transit and at rest using industry-standard AES-256 encryption.</p>
            </div>

            <div className="text-center p-8 bg-white rounded-2xl shadow-lg">
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Lock className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Privacy Compliance</h3>
              <p className="text-gray-600">Full compliance with Australian Privacy Principles and GDPR requirements.</p>
            </div>

            <div className="text-center p-8 bg-white rounded-2xl shadow-lg">
              <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Globe className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Australian Hosted</h3>
              <p className="text-gray-600">All data is stored and processed within Australia using secure cloud infrastructure.</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-br from-primary-600 via-primary-700 to-indigo-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
            Join Our Mission
          </h2>
          
          <p className="text-xl md:text-2xl text-primary-100 mb-12 max-w-3xl mx-auto leading-relaxed">
            Be part of the property intelligence revolution. Start making smarter decisions today.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              href="/auth/register"
              className="group relative overflow-hidden bg-white text-primary-600 hover:text-primary-700 font-bold px-12 py-5 rounded-2xl transition-all duration-300 shadow-2xl hover:shadow-white/25 transform hover:-translate-y-1"
            >
              <span className="relative text-lg">Start Free Trial</span>
            </Link>
            <Link
              href="/contact"
              className="group bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white font-bold px-12 py-5 rounded-2xl border-2 border-white/30 hover:border-white/50 transition-all duration-300 transform hover:-translate-y-1"
            >
              <span className="text-lg">Contact Us</span>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
