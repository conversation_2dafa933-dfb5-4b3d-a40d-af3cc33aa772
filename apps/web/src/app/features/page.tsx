'use client';

import { BarChart3, Calculator, Eye, MapPin, Shield, TrendingUp, Users, Zap } from 'lucide-react';
import Link from 'next/link';

export default function FeaturesPage() {
  const features = [
    {
      icon: <Calculator className="w-8 h-8" />,
      title: "Real-time AI Valuations",
      description: "Get instant, accurate property valuations in under 3 seconds using our proprietary AI algorithm trained on millions of Australian property transactions.",
      benefits: [
        "98.7% accuracy rate validated against actual sales",
        "Instant results in under 3 seconds",
        "Confidence ranges and data quality scores",
        "Historical valuation tracking"
      ],
      gradient: "from-green-500 to-emerald-600"
    },
    {
      icon: <TrendingUp className="w-8 h-8" />,
      title: "AI Predictions & Forecasting",
      description: "Leverage advanced machine learning to predict property values 12 and 24 months into the future with confidence intervals.",
      benefits: [
        "12 and 24-month value predictions",
        "Confidence intervals and risk assessment",
        "Market cycle analysis",
        "Economic factor modeling"
      ],
      gradient: "from-blue-500 to-indigo-600"
    },
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: "18+ Premium Data Sources",
      description: "Access comprehensive market intelligence from 18+ premium data sources updated in real-time for the most accurate insights.",
      benefits: [
        "Property sales & listing data",
        "Development applications & planning",
        "Economic indicators & demographics",
        "Infrastructure projects & transport"
      ],
      gradient: "from-purple-500 to-pink-600"
    },
    {
      icon: <MapPin className="w-8 h-8" />,
      title: "Development Analysis Tools",
      description: "Discover hidden development opportunities with AI-powered analysis of zoning, planning applications, and infrastructure developments.",
      benefits: [
        "3D building envelope visualization",
        "Development potential scoring",
        "DA precedent analysis",
        "ROI and feasibility calculations"
      ],
      gradient: "from-orange-500 to-red-600"
    },
    {
      icon: <Eye className="w-8 h-8" />,
      title: "Market Intelligence Dashboard",
      description: "Comprehensive market insights with heat maps, trend analysis, and emerging suburb detection powered by real-time data.",
      benefits: [
        "Interactive market heat maps",
        "Suburb trend analysis",
        "Market movers tracking",
        "Emerging opportunities detection"
      ],
      gradient: "from-teal-500 to-cyan-600"
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Portfolio Management",
      description: "Track multiple properties, analyze portfolio performance, and receive personalized insights and alerts.",
      benefits: [
        "Multi-property tracking",
        "Performance analytics",
        "Automated alerts & notifications",
        "Portfolio diversification analysis"
      ],
      gradient: "from-indigo-500 to-purple-600"
    }
  ];

  return (
    <div className="bg-gradient-to-br from-blue-50 via-white to-indigo-50 min-h-screen">
      {/* Hero Section */}
      <section className="relative py-24 px-4 sm:px-6 lg:px-8 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-50/50 via-transparent to-indigo-50/50"></div>
        
        <div className="relative max-w-7xl mx-auto text-center">
          <div className="mb-8">
            <div className="inline-flex items-center px-4 py-2 bg-primary-50 border border-primary-200 rounded-full text-primary-700 text-sm font-medium mb-6">
              <span className="w-2 h-2 bg-primary-500 rounded-full mr-2 animate-pulse"></span>
              Comprehensive Property Intelligence Platform
            </div>
          </div>

          <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-8 leading-tight">
            Powerful Features for
            <span className="block bg-gradient-to-r from-primary-600 to-indigo-600 bg-clip-text text-transparent">
              Property Professionals
            </span>
          </h1>

          <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
            Discover all the advanced features that make Revalu the most comprehensive 
            property intelligence platform in Australia.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/auth/register"
              className="group relative overflow-hidden bg-gradient-to-r from-primary-600 to-indigo-600 hover:from-primary-700 hover:to-indigo-700 text-white font-bold px-10 py-4 rounded-2xl transition-all duration-300 shadow-2xl hover:shadow-primary-500/25 transform hover:-translate-y-1"
            >
              <span className="relative text-lg">Start Free Trial</span>
            </Link>
            <Link
              href="/pricing"
              className="group bg-white hover:bg-gray-50 text-gray-700 hover:text-primary-600 font-bold px-10 py-4 rounded-2xl border-2 border-gray-200 hover:border-primary-300 transition-all duration-300 transform hover:-translate-y-1"
            >
              <span className="text-lg">View Pricing</span>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-3xl shadow-xl border border-gray-100 p-8 hover:shadow-2xl transition-all duration-300">
                <div className={`w-16 h-16 bg-gradient-to-br ${feature.gradient} rounded-2xl flex items-center justify-center text-white mb-6`}>
                  {feature.icon}
                </div>
                
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {feature.title}
                </h3>
                
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {feature.description}
                </p>
                
                <ul className="space-y-3">
                  {feature.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex} className="flex items-start">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <span className="text-gray-700">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Accuracy & Trust Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Trusted by Property Professionals
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Industry-leading accuracy and reliability you can count on for critical investment decisions.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-8 bg-white rounded-2xl shadow-lg">
              <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Shield className="w-8 h-8 text-green-600" />
              </div>
              <div className="text-4xl font-bold text-green-600 mb-2">98.7%</div>
              <div className="text-gray-600 font-medium">Accuracy Rate</div>
              <div className="text-sm text-gray-500 mt-2">Validated against actual sales data</div>
            </div>

            <div className="text-center p-8 bg-white rounded-2xl shadow-lg">
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Zap className="w-8 h-8 text-blue-600" />
              </div>
              <div className="text-4xl font-bold text-blue-600 mb-2">&lt;3s</div>
              <div className="text-gray-600 font-medium">Response Time</div>
              <div className="text-sm text-gray-500 mt-2">Instant AI-powered results</div>
            </div>

            <div className="text-center p-8 bg-white rounded-2xl shadow-lg">
              <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Users className="w-8 h-8 text-purple-600" />
              </div>
              <div className="text-4xl font-bold text-purple-600 mb-2">10,000+</div>
              <div className="text-gray-600 font-medium">Active Users</div>
              <div className="text-sm text-gray-500 mt-2">Property professionals trust Revalu</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-br from-primary-600 via-primary-700 to-indigo-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
            Ready to Get Started?
          </h2>
          
          <p className="text-xl md:text-2xl text-primary-100 mb-12 max-w-3xl mx-auto leading-relaxed">
            Join thousands of property professionals using Revalu for smarter investment decisions.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              href="/auth/register"
              className="group relative overflow-hidden bg-white text-primary-600 hover:text-primary-700 font-bold px-12 py-5 rounded-2xl transition-all duration-300 shadow-2xl hover:shadow-white/25 transform hover:-translate-y-1"
            >
              <span className="relative text-lg">Start Free Trial</span>
            </Link>
            <Link
              href="/contact"
              className="group bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white font-bold px-12 py-5 rounded-2xl border-2 border-white/30 hover:border-white/50 transition-all duration-300 transform hover:-translate-y-1"
            >
              <span className="text-lg">Contact Sales</span>
            </Link>
          </div>

          <div className="mt-12 text-primary-200 text-sm">
            ✓ No credit card required • ✓ 14-day free trial • ✓ Cancel anytime
          </div>
        </div>
      </section>
    </div>
  );
}
