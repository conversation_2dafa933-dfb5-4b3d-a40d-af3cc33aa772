'use client';

import { useState } from 'react';
import { Check, X, Star, Zap, Users, Building, Crown, Sparkles } from 'lucide-react';
import Link from 'next/link';

interface PlanFeature {
  name: string;
  included: boolean;
  description?: string;
}

interface Plan {
  id: string;
  name: string;
  description: string;
  price: {
    monthly: number;
    yearly: number;
  };
  yearlyDiscount?: number;
  popular?: boolean;
  icon: React.ReactNode;
  features: PlanFeature[];
  cta: string;
  ctaVariant: 'primary' | 'secondary' | 'outline';
  limits: {
    searches: string;
    properties: string;
    reports: string;
    support: string;
  };
}

const plans: Plan[] = [
  {
    id: 'revalu-access',
    name: 'Revalu Access',
    description: 'Perfect for exploring property intelligence',
    price: { monthly: 0, yearly: 0 },
    icon: <Sparkles className="w-6 h-6" />,
    features: [
      { name: '4 searches per year', included: true },
      { name: 'High-level valuation', included: true },
      { name: 'Suburb context', included: true },
      { name: 'Basic property intelligence', included: true },
      { name: 'Property tracking', included: false },
      { name: 'Historical trends', included: false },
      { name: 'Export reports', included: false },
      { name: 'Email alerts', included: false },
    ],
    limits: {
      searches: '4/year',
      properties: '0',
      reports: 'View only',
      support: 'Community',
    },
    cta: 'Get Started Free',
    ctaVariant: 'outline',
  },
  {
    id: 'home-plus',
    name: 'Home+',
    description: 'Monitor your home and neighborhood',
    price: { monthly: 19, yearly: 193 },
    yearlyDiscount: 15,
    popular: true,
    icon: <Star className="w-6 h-6" />,
    features: [
      { name: '10 searches per month', included: true },
      { name: 'Track your PPR + 2 others', included: true },
      { name: 'Historical valuation trends', included: true },
      { name: 'Email & push alerts', included: true },
      { name: 'Detailed property intelligence', included: true },
      { name: 'Market insights', included: true },
      { name: 'Export reports', included: false },
      { name: 'Portfolio analytics', included: false },
    ],
    limits: {
      searches: '10/month',
      properties: '3 tracked',
      reports: 'View only',
      support: 'Email',
    },
    cta: 'Start Free Trial',
    ctaVariant: 'primary',
  },
  {
    id: 'investor-plus',
    name: 'Investor+',
    description: 'Advanced tools for property investors',
    price: { monthly: 49, yearly: 499 },
    yearlyDiscount: 15,
    icon: <Zap className="w-6 h-6" />,
    features: [
      { name: '20 searches per month', included: true },
      { name: 'Track up to 5 properties', included: true },
      { name: 'Add notes and tags', included: true },
      { name: 'Portfolio view', included: true },
      { name: 'Export PDF reports', included: true },
      { name: 'Advanced analytics', included: true },
      { name: 'Investment scoring', included: true },
      { name: 'Development potential', included: true },
    ],
    limits: {
      searches: '20/month',
      properties: '5 tracked',
      reports: 'PDF export',
      support: 'Email',
    },
    cta: 'Start Free Trial',
    ctaVariant: 'primary',
  },
  {
    id: 'agent-pro',
    name: 'Agent Pro',
    description: 'Professional tools for real estate agents',
    price: { monthly: 149, yearly: 1432 },
    yearlyDiscount: 20,
    icon: <Building className="w-6 h-6" />,
    features: [
      { name: '100 searches per month', included: true },
      { name: 'CMA-ready valuations', included: true },
      { name: 'Custom comparables', included: true },
      { name: 'PDF/CSV export', included: true },
      { name: 'Share reports with clients', included: true },
      { name: 'Priority support', included: true },
      { name: 'Professional branding', included: true },
      { name: 'Client management', included: true },
    ],
    limits: {
      searches: '100/month',
      properties: '20 tracked',
      reports: 'Full export',
      support: 'Priority',
    },
    cta: 'Start Free Trial',
    ctaVariant: 'primary',
  },
  {
    id: 'team-plan',
    name: 'Team Plan',
    description: 'Collaboration for real estate teams',
    price: { monthly: 399, yearly: 3830 },
    yearlyDiscount: 20,
    icon: <Users className="w-6 h-6" />,
    features: [
      { name: 'Unlimited searches', included: true },
      { name: 'Up to 5 team members', included: true },
      { name: 'Branded reports', included: true },
      { name: 'Team workspace', included: true },
      { name: 'Admin dashboard', included: true },
      { name: 'Usage tracking', included: true },
      { name: 'Zoning overlays', included: true },
      { name: 'Planning data', included: true },
    ],
    limits: {
      searches: 'Unlimited',
      properties: 'Unlimited',
      reports: 'White-label',
      support: 'Priority',
    },
    cta: 'Contact Sales',
    ctaVariant: 'primary',
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'Custom solutions for large organizations',
    price: { monthly: 0, yearly: 0 },
    icon: <Crown className="w-6 h-6" />,
    features: [
      { name: 'Unlimited everything', included: true },
      { name: 'Data integrations', included: true },
      { name: 'Full API access', included: true },
      { name: 'Training & onboarding', included: true },
      { name: 'SLAs & dedicated support', included: true },
      { name: 'Usage insights', included: true },
      { name: 'Custom development', included: true },
      { name: 'Advanced admin', included: true },
    ],
    limits: {
      searches: 'Unlimited',
      properties: 'Unlimited',
      reports: 'Custom',
      support: 'Dedicated',
    },
    cta: 'Contact Sales',
    ctaVariant: 'secondary',
  },
];

export default function PricingPage() {
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');

  const formatPrice = (price: number) => {
    if (price === 0) return 'Free';
    return `$${price}`;
  };

  const getButtonClass = (variant: string, popular?: boolean) => {
    if (popular) {
      return 'w-full btn-primary';
    }
    
    switch (variant) {
      case 'primary':
        return 'w-full btn-primary';
      case 'secondary':
        return 'w-full bg-gray-900 text-white hover:bg-gray-800 px-6 py-3 rounded-lg font-medium transition-colors';
      case 'outline':
        return 'w-full btn-outline';
      default:
        return 'w-full btn-primary';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Choose Your Plan
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Unlock the power of AI-driven property intelligence. From homeowners to enterprise teams, 
              we have the perfect plan for your property analysis needs.
            </p>
          </div>

          {/* Billing Toggle */}
          <div className="flex justify-center mt-8">
            <div className="bg-gray-100 p-1 rounded-lg">
              <button
                onClick={() => setBillingPeriod('monthly')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  billingPeriod === 'monthly'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Monthly
              </button>
              <button
                onClick={() => setBillingPeriod('yearly')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  billingPeriod === 'yearly'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Yearly
                <span className="ml-1 text-xs text-green-600 font-semibold">Save up to 20%</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`relative bg-white rounded-2xl shadow-lg border-2 transition-all duration-200 hover:shadow-xl ${
                plan.popular
                  ? 'border-primary-500 ring-2 ring-primary-200'
                  : 'border-gray-200 hover:border-primary-300'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="p-8">
                {/* Plan Header */}
                <div className="text-center mb-8">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg mb-4 ${
                    plan.popular ? 'bg-primary-100 text-primary-600' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {plan.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  <p className="text-gray-600 mb-4">{plan.description}</p>
                  
                  <div className="mb-4">
                    <span className="text-4xl font-bold text-gray-900">
                      {formatPrice(plan.price[billingPeriod])}
                    </span>
                    {plan.price[billingPeriod] > 0 && (
                      <span className="text-gray-600 ml-1">
                        /{billingPeriod === 'monthly' ? 'month' : 'year'}
                      </span>
                    )}
                  </div>

                  {billingPeriod === 'yearly' && plan.yearlyDiscount && plan.price.monthly > 0 && (
                    <div className="text-sm text-green-600 font-medium">
                      Save {plan.yearlyDiscount}% with yearly billing
                    </div>
                  )}
                </div>

                {/* Quick Stats */}
                <div className="grid grid-cols-2 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <div className="text-sm font-medium text-gray-900">{plan.limits.searches}</div>
                    <div className="text-xs text-gray-600">Searches</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm font-medium text-gray-900">{plan.limits.properties}</div>
                    <div className="text-xs text-gray-600">Properties</div>
                  </div>
                </div>

                {/* Features */}
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      {feature.included ? (
                        <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                      ) : (
                        <X className="w-5 h-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" />
                      )}
                      <span className={`text-sm ${
                        feature.included ? 'text-gray-900' : 'text-gray-500'
                      }`}>
                        {feature.name}
                      </span>
                    </li>
                  ))}
                </ul>

                {/* CTA Button */}
                <Link
                  href={plan.id === 'enterprise' || plan.id === 'team-plan' ? '/contact' : '/auth/register'}
                  className={getButtonClass(plan.ctaVariant, plan.popular)}
                >
                  {plan.cta}
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* FAQ Section */}
        <div className="mt-20 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">
            Frequently Asked Questions
          </h2>
          <div className="max-w-3xl mx-auto">
            <div className="space-y-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Can I change plans anytime?
                </h3>
                <p className="text-gray-600">
                  Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, 
                  and we'll prorate any billing adjustments.
                </p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  What happens if I exceed my search limit?
                </h3>
                <p className="text-gray-600">
                  You'll be prompted to upgrade your plan to continue searching. We'll never charge you 
                  without permission - you'll always have control over your subscription.
                </p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Do you offer a free trial?
                </h3>
                <p className="text-gray-600">
                  Yes! All paid plans come with a 14-day free trial. No credit card required to start, 
                  and you can cancel anytime during the trial period.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="mt-16 text-center bg-white p-8 rounded-2xl shadow-lg">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Need a Custom Solution?
          </h2>
          <p className="text-gray-600 mb-6">
            Contact our sales team for enterprise pricing, custom integrations, 
            or if you have specific requirements we can help with.
          </p>
          <Link href="/contact" className="btn-primary">
            Contact Sales
          </Link>
        </div>
      </div>
    </div>
  );
}
